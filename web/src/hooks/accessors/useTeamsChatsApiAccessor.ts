import React from 'react';
import { EventReportType, EventReporter } from '@avanade-teams/app-insights-reporter';
import { WeakTokenProvider } from '../../types/TokenProvider';
import { Reporters } from '../../types/Reporters';
import { fetchUrlRes } from '../../utilities/commonFunction';
import { getUniqueNameByToken } from '../../utilities/token/jwt';
import environment from '../../utilities/environment';

// エラー定数
export const UseTeamsChatsApiAccessorError = {
  MISSING_PARAMS: 'MISSING_PARAMS',
  NO_TOKENS: 'NO_TOKENS',
  IS_OFFLINE_OR_SOMETHING_WRONG: 'IS_OFFLINE_OR_SOMETHING_WRONG',
};

// API URL プレフィックス
const PREFIX = environment.REACT_APP_API_URL ?? '/api';

// Teams チャット設定の型定義
export interface ITeamsChatsRequest {
  chatId?: string;
  channelId?: string;
  chatType: 'チャット' | 'チャネル';
  name: string;
}

// API 関数の型定義
export type PostTeamsChatsApi = (request: ITeamsChatsRequest) => Promise<void>;

export type UseTeamsChatsApiReturnType = {
  postTeamsChatsApi: PostTeamsChatsApi | undefined;
};

/**
 * Teams チャット設定の POST URL を作成する
 * @param userId
 */
export function createPostTeamsChatsUrl(userId: string): string {
  if (!userId) return '';
  return `${PREFIX}/users/${userId}/teams-chats`;
}

/**
 * TeamsChatTableへ登録へ行く
 */
async function postTeamsChatsApiImpl(
  tokenProvider: WeakTokenProvider,
  request: ITeamsChatsRequest,
  report: EventReporter,
): Promise<void> {
  if (!tokenProvider) return Promise.reject(new Error(UseTeamsChatsApiAccessorError.NO_TOKENS));

  // パラメータ検証
  if (!request.name || !request.chatType) {
    return Promise.reject(new Error(UseTeamsChatsApiAccessorError.MISSING_PARAMS));
  }

  if (request.chatType === 'チャット' && !request.chatId) {
    return Promise.reject(new Error(UseTeamsChatsApiAccessorError.MISSING_PARAMS));
  }

  if (request.chatType === 'チャネル' && !request.channelId) {
    return Promise.reject(new Error(UseTeamsChatsApiAccessorError.MISSING_PARAMS));
  }

  const [token, uId] = await getUniqueNameByToken(tokenProvider);

  try {
    const res = await fetchUrlRes(
      token,
      'POST',
      createPostTeamsChatsUrl(uId),
      JSON.stringify(request),
    );

    if (res.status !== 201 && res.status !== 200) {
      report({
        type: EventReportType.SYS_ERROR,
        name: UseTeamsChatsApiAccessorError.IS_OFFLINE_OR_SOMETHING_WRONG,
        error: new Error(res.statusText),
      });
      throw new Error(`API Error: ${res.status} ${res.statusText}`);
    }
  } catch (e) {
    report({
      type: EventReportType.SYS_ERROR,
      name: UseTeamsChatsApiAccessorError.IS_OFFLINE_OR_SOMETHING_WRONG,
      error: e as Error,
    });
    throw e;
  }

  return Promise.resolve();
}

/**
 * TeamsChatTableへ接続するアクセサー
 * @param tokenProvider
 * @param reporters
 */
const useTeamsChatsApiAccessor = (
  tokenProvider: WeakTokenProvider,
  reporters: Reporters,
): UseTeamsChatsApiReturnType => {
  const [report] = reporters;

  const postTeamsChatsApi: PostTeamsChatsApi = React.useCallback(
    async (request: ITeamsChatsRequest) => postTeamsChatsApiImpl(
      tokenProvider, request, report,
    ), [tokenProvider, report],
  );

  return {
    postTeamsChatsApi: tokenProvider ? postTeamsChatsApi : undefined,
  };
};

export default useTeamsChatsApiAccessor;
